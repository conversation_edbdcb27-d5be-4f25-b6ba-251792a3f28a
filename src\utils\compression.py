"""
File compression utilities for reducing transfer sizes.
"""

import os
import gzip
import bz2
import lzma
import zipfile
import tempfile
import shutil
from pathlib import Path
from typing import Optional, Tuple, Dict, Any, List
from enum import Enum
from dataclasses import dataclass
from src.utils.logger import get_logger
from src.utils.file_utils import FileUtils


class CompressionMethod(Enum):
    """Available compression methods."""
    NONE = "none"
    GZIP = "gzip"
    BZIP2 = "bzip2"
    LZMA = "lzma"
    ZIP = "zip"
    AUTO = "auto"


@dataclass
class CompressionResult:
    """Result of compression operation."""
    original_size: int
    compressed_size: int
    compression_ratio: float
    method_used: CompressionMethod
    compressed_file: str
    success: bool
    error_message: Optional[str] = None
    
    @property
    def size_reduction_percent(self) -> float:
        """Calculate size reduction percentage."""
        if self.original_size == 0:
            return 0.0
        return ((self.original_size - self.compressed_size) / self.original_size) * 100


class FileCompressor:
    """
    Advanced file compression system with multiple algorithms.
    """
    
    # File types that compress well
    COMPRESSIBLE_EXTENSIONS = {
        '.txt', '.log', '.csv', '.json', '.xml', '.html', '.css', '.js',
        '.py', '.java', '.cpp', '.c', '.h', '.md', '.sql', '.yaml', '.yml',
        '.ini', '.cfg', '.conf', '.properties', '.bat', '.sh', '.ps1'
    }
    
    # File types that are already compressed
    ALREADY_COMPRESSED = {
        '.zip', '.rar', '.7z', '.gz', '.bz2', '.xz', '.tar', '.tgz',
        '.jpg', '.jpeg', '.png', '.gif', '.mp3', '.mp4', '.avi', '.mkv',
        '.pdf', '.docx', '.xlsx', '.pptx', '.odt', '.ods', '.odp'
    }
    
    # Minimum file size to consider compression (in bytes)
    MIN_COMPRESSION_SIZE = 1024  # 1KB
    
    def __init__(self, temp_dir: Optional[str] = None):
        """
        Initialize the file compressor.
        
        Args:
            temp_dir: Directory for temporary files
        """
        self.logger = get_logger()
        self.temp_dir = Path(temp_dir) if temp_dir else Path(tempfile.gettempdir()) / "filetransfer_compression"
        self.temp_dir.mkdir(exist_ok=True)
        
        # Compression statistics
        self.stats = {
            'files_compressed': 0,
            'total_original_size': 0,
            'total_compressed_size': 0,
            'total_time_saved': 0.0
        }
    
    def should_compress(self, file_path: str) -> bool:
        """
        Determine if a file should be compressed.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file should be compressed
        """
        path = Path(file_path)
        
        # Check file size
        file_size = FileUtils.get_file_size(file_path)
        if file_size < self.MIN_COMPRESSION_SIZE:
            return False
        
        # Check file extension
        extension = path.suffix.lower()
        
        # Don't compress already compressed files
        if extension in self.ALREADY_COMPRESSED:
            return False
        
        # Compress text-based files
        if extension in self.COMPRESSIBLE_EXTENSIONS:
            return True
        
        # For unknown extensions, try a small sample
        if file_size > 10 * 1024 * 1024:  # Files larger than 10MB
            return self._test_compression_sample(file_path)
        
        return True  # Default to trying compression
    
    def compress_file(self, file_path: str, method: CompressionMethod = CompressionMethod.AUTO) -> CompressionResult:
        """
        Compress a file using the specified method.
        
        Args:
            file_path: Path to the file to compress
            method: Compression method to use
            
        Returns:
            CompressionResult with details about the compression
        """
        try:
            original_size = FileUtils.get_file_size(file_path)
            
            if not self.should_compress(file_path):
                # Return original file without compression
                return CompressionResult(
                    original_size=original_size,
                    compressed_size=original_size,
                    compression_ratio=1.0,
                    method_used=CompressionMethod.NONE,
                    compressed_file=file_path,
                    success=True
                )
            
            # Determine best compression method
            if method == CompressionMethod.AUTO:
                method = self._choose_best_method(file_path)
            
            # Compress the file
            compressed_file = self._compress_with_method(file_path, method)
            compressed_size = FileUtils.get_file_size(compressed_file)
            
            # Calculate compression ratio
            compression_ratio = compressed_size / original_size if original_size > 0 else 1.0
            
            # If compression didn't help much, use original file
            if compression_ratio > 0.95:  # Less than 5% reduction
                os.unlink(compressed_file)
                return CompressionResult(
                    original_size=original_size,
                    compressed_size=original_size,
                    compression_ratio=1.0,
                    method_used=CompressionMethod.NONE,
                    compressed_file=file_path,
                    success=True
                )
            
            # Update statistics
            self.stats['files_compressed'] += 1
            self.stats['total_original_size'] += original_size
            self.stats['total_compressed_size'] += compressed_size
            
            self.logger.info(f"Compressed {Path(file_path).name}: {FileUtils.format_file_size(original_size)} → {FileUtils.format_file_size(compressed_size)} ({compression_ratio:.2%})")
            
            return CompressionResult(
                original_size=original_size,
                compressed_size=compressed_size,
                compression_ratio=compression_ratio,
                method_used=method,
                compressed_file=compressed_file,
                success=True
            )
            
        except Exception as e:
            self.logger.error(f"Compression failed for {file_path}: {e}")
            return CompressionResult(
                original_size=FileUtils.get_file_size(file_path),
                compressed_size=0,
                compression_ratio=1.0,
                method_used=method,
                compressed_file="",
                success=False,
                error_message=str(e)
            )
    
    def decompress_file(self, compressed_file: str, output_path: str, method: CompressionMethod) -> bool:
        """
        Decompress a file.
        
        Args:
            compressed_file: Path to the compressed file
            output_path: Path where to save the decompressed file
            method: Compression method that was used
            
        Returns:
            True if decompression was successful
        """
        try:
            if method == CompressionMethod.NONE:
                # Just copy the file
                shutil.copy2(compressed_file, output_path)
                return True
            
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            if method == CompressionMethod.GZIP:
                with gzip.open(compressed_file, 'rb') as f_in:
                    with open(output_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            
            elif method == CompressionMethod.BZIP2:
                with bz2.open(compressed_file, 'rb') as f_in:
                    with open(output_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            
            elif method == CompressionMethod.LZMA:
                with lzma.open(compressed_file, 'rb') as f_in:
                    with open(output_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            
            elif method == CompressionMethod.ZIP:
                with zipfile.ZipFile(compressed_file, 'r') as zip_file:
                    # Extract the first file (should be only one)
                    names = zip_file.namelist()
                    if names:
                        with zip_file.open(names[0]) as f_in:
                            with open(output_path, 'wb') as f_out:
                                shutil.copyfileobj(f_in, f_out)
            
            self.logger.info(f"Decompressed {Path(compressed_file).name} to {Path(output_path).name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Decompression failed: {e}")
            return False
    
    def _choose_best_method(self, file_path: str) -> CompressionMethod:
        """
        Choose the best compression method for a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Best compression method
        """
        path = Path(file_path)
        extension = path.suffix.lower()
        file_size = FileUtils.get_file_size(file_path)
        
        # For text files, use LZMA for best compression
        if extension in {'.txt', '.log', '.csv', '.json', '.xml', '.html', '.md'}:
            return CompressionMethod.LZMA
        
        # For code files, use GZIP for good compression and speed
        if extension in {'.py', '.java', '.cpp', '.c', '.js', '.css'}:
            return CompressionMethod.GZIP
        
        # For large files, use GZIP for speed
        if file_size > 100 * 1024 * 1024:  # 100MB
            return CompressionMethod.GZIP
        
        # Default to LZMA for best compression
        return CompressionMethod.LZMA
    
    def _compress_with_method(self, file_path: str, method: CompressionMethod) -> str:
        """
        Compress file with specific method.
        
        Args:
            file_path: Path to the file
            method: Compression method
            
        Returns:
            Path to compressed file
        """
        input_path = Path(file_path)
        
        if method == CompressionMethod.GZIP:
            output_path = self.temp_dir / f"{input_path.name}.gz"
            with open(file_path, 'rb') as f_in:
                with gzip.open(output_path, 'wb', compresslevel=6) as f_out:
                    shutil.copyfileobj(f_in, f_out)
        
        elif method == CompressionMethod.BZIP2:
            output_path = self.temp_dir / f"{input_path.name}.bz2"
            with open(file_path, 'rb') as f_in:
                with bz2.open(output_path, 'wb', compresslevel=6) as f_out:
                    shutil.copyfileobj(f_in, f_out)
        
        elif method == CompressionMethod.LZMA:
            output_path = self.temp_dir / f"{input_path.name}.xz"
            with open(file_path, 'rb') as f_in:
                with lzma.open(output_path, 'wb', preset=6) as f_out:
                    shutil.copyfileobj(f_in, f_out)
        
        elif method == CompressionMethod.ZIP:
            output_path = self.temp_dir / f"{input_path.name}.zip"
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zip_file:
                zip_file.write(file_path, input_path.name)
        
        else:
            raise ValueError(f"Unsupported compression method: {method}")
        
        return str(output_path)
    
    def _test_compression_sample(self, file_path: str) -> bool:
        """
        Test compression on a small sample of the file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if compression seems beneficial
        """
        try:
            sample_size = min(64 * 1024, FileUtils.get_file_size(file_path))  # 64KB sample
            
            with open(file_path, 'rb') as f:
                sample_data = f.read(sample_size)
            
            # Test GZIP compression on sample
            compressed_sample = gzip.compress(sample_data, compresslevel=1)
            compression_ratio = len(compressed_sample) / len(sample_data)
            
            # If we get less than 80% of original size, it's worth compressing
            return compression_ratio < 0.8
            
        except Exception:
            return False
    
    def get_compression_stats(self) -> Dict[str, Any]:
        """Get compression statistics."""
        stats = self.stats.copy()
        
        if stats['total_original_size'] > 0:
            stats['overall_compression_ratio'] = stats['total_compressed_size'] / stats['total_original_size']
            stats['total_size_saved'] = stats['total_original_size'] - stats['total_compressed_size']
            stats['size_reduction_percent'] = (stats['total_size_saved'] / stats['total_original_size']) * 100
        else:
            stats['overall_compression_ratio'] = 1.0
            stats['total_size_saved'] = 0
            stats['size_reduction_percent'] = 0.0
        
        return stats
    
    def cleanup_temp_files(self):
        """Clean up temporary compression files."""
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                self.temp_dir.mkdir(exist_ok=True)
        except Exception as e:
            self.logger.warning(f"Failed to cleanup temp files: {e}")


# Global compressor instance
global_compressor = FileCompressor()
